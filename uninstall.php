<?php
/**
 * Plugin Ranker Uninstall Script
 * 
 * This file is executed when the plugin is deleted from WordPress admin.
 * It cleans up all plugin data from the database.
 */

// Prevent direct access
if (!defined('WP_UNINSTALL_PLUGIN')) {
    exit;
}

/**
 * Clean up plugin data
 */
function plugin_ranker_uninstall() {
    // Define option names to delete
    $options_to_delete = array(
        'plugin_ranker_plugin_ranks',
        'plugin_ranker_pinned_plugins',
        'plugin_ranker_plugin_companies',
        'plugin_ranker_version'
    );
    
    // Delete options for single site
    if (!is_multisite()) {
        foreach ($options_to_delete as $option) {
            delete_option($option);
        }
    } else {
        // Delete options for multisite
        global $wpdb;
        
        // Get all blog IDs
        $blog_ids = $wpdb->get_col("SELECT blog_id FROM $wpdb->blogs");
        
        foreach ($blog_ids as $blog_id) {
            switch_to_blog($blog_id);
            
            foreach ($options_to_delete as $option) {
                delete_option($option);
            }
            
            restore_current_blog();
        }
        
        // Also delete network-wide options if any
        foreach ($options_to_delete as $option) {
            delete_site_option($option);
        }
    }
    
    // Clear any cached data
    wp_cache_flush();
}

// Execute cleanup
plugin_ranker_uninstall();
