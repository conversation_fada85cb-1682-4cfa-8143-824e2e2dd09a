<?php
/**
 * Plugin Name: Plugin Ranker
 * Plugin URI: https://github.com/your-username/plugin-ranker
 * Description: Enhance the WordPress admin Plugins page with custom ranking, pinning, and company sorting features.
 * Version: 1.0.0
 * Author: <PERSON><PERSON><PERSON>(WPDevloper SQA Team)
 * Author URI: https://wpdevloper.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: plugin-ranker
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.8
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('PLUGIN_RANKER_VERSION', '1.0.0');
define('PLUGIN_RANKER_PLUGIN_FILE', __FILE__);
define('PLUGIN_RANKER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('PLUGIN_RANKER_PLUGIN_URL', plugin_dir_url(__FILE__));
define('PLUGIN_RANKER_PLUGIN_BASENAME', plugin_basename(__FILE__));

/**
 * Main Plugin Ranker Class
 */
class PluginRanker {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Activation and deactivation hooks
        register_activation_hook(PLUGIN_RANKER_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(PLUGIN_RANKER_PLUGIN_FILE, array($this, 'deactivate'));
        
        // Initialize plugin
        add_action('init', array($this, 'init'));
        
        // Admin hooks
        if (is_admin()) {
            add_action('admin_init', array($this, 'admin_init'));
            add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
            add_action('admin_notices', array($this, 'show_activation_notice'));

            // Add settings link to plugin page
            add_filter('plugin_action_links_' . PLUGIN_RANKER_PLUGIN_BASENAME, array($this, 'add_settings_link'));
        }
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Set default options
        $default_options = array(
            'plugin_ranks' => array(),
            'pinned_plugins' => array(),
            'plugin_companies' => array(),
            'version' => PLUGIN_RANKER_VERSION
        );

        // Add options if they don't exist
        foreach ($default_options as $option_name => $default_value) {
            $option_key = 'plugin_ranker_' . $option_name;
            if (false === get_option($option_key)) {
                add_option($option_key, $default_value);
            }
        }

        // Set activation flag for debugging
        add_option('plugin_ranker_activated', time());

        // Add activation notice
        add_option('plugin_ranker_show_activation_notice', true);

        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('plugin-ranker', false, dirname(PLUGIN_RANKER_PLUGIN_BASENAME) . '/languages');
        
        // Include required files
        $this->include_files();
    }
    
    /**
     * Admin initialization
     */
    public function admin_init() {
        // Include admin-specific files
        $this->include_admin_files();

        // Debug: Log admin initialization
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Plugin Ranker: Admin initialized for user with capabilities: ' .
                (current_user_can('manage_options') ? 'manage_options=YES' : 'manage_options=NO'));
        }
    }
    
    /**
     * Include required files
     */
    private function include_files() {
        // Only include data manager for storing rankings
        require_once PLUGIN_RANKER_PLUGIN_DIR . 'includes/class-plugin-data-manager.php';
    }
    
    /**
     * Include admin-specific files
     */
    private function include_admin_files() {
        // Include and initialize tools page
        require_once PLUGIN_RANKER_PLUGIN_DIR . 'admin/class-settings-page.php';
        new PluginRanker_Settings_Page();

        // Enable diagnostic page for troubleshooting
        if (defined('WP_DEBUG') && WP_DEBUG) {
            PluginRanker_Settings_Page::create_diagnostic_page();

            // Also include simple test page
            require_once PLUGIN_RANKER_PLUGIN_DIR . 'admin/class-simple-test-page.php';
            new PluginRanker_Simple_Test_Page();
        }
    }
    
    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // No scripts needed for plugins page since we're not modifying it
        // Settings page handles its own scripts
        return;
    }

    /**
     * Add settings link to plugin page
     */
    public function add_settings_link($links) {
        // Try multiple possible URLs for the settings page
        $possible_urls = array(
            admin_url('tools.php?page=plugin-ranker-tools'),
            admin_url('options-general.php?page=plugin-ranker-settings'),
            admin_url('admin.php?page=plugin-ranker-main')
        );

        $settings_link = '<a href="' . esc_url($possible_urls[0]) . '">' . __('Settings') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }

    /**
     * Show activation notice
     */
    public function show_activation_notice() {
        if (get_option('plugin_ranker_show_activation_notice')) {
            $settings_url = esc_url(admin_url('tools.php?page=plugin-ranker-tools'));
            ?>
            <div class="notice notice-success is-dismissible">
                <p>
                    <strong>Plugin Ranker activated!</strong>
                    <a href="<?php echo $settings_url; ?>" class="button button-primary">Go to Settings</a>
                </p>
            </div>
            <?php
            delete_option('plugin_ranker_show_activation_notice');
        }
    }

    /**
     * Debug function
     */
    public static function debug_log($message, $data = null) {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            $log_message = 'Plugin Ranker: ' . $message;
            if ($data !== null) {
                $log_message .= ' | Data: ' . print_r($data, true);
            }
            error_log($log_message);
        }
    }
}

/**
 * Initialize the plugin
 */
function plugin_ranker_init() {
    return PluginRanker::get_instance();
}

// Start the plugin
plugin_ranker_init();
