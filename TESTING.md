# Plugin Ranker Testing Guide

## Quick Testing Steps

### 1. Enable Debug Mode
Add these lines to your `wp-config.php` file:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### 2. Activate the Plugin
1. Go to **Plugins** → **Installed Plugins**
2. Find "Plugin Ranker" and click **Activate**
3. You should see debug information at the top of the page if debug mode is enabled

### 3. Check the Plugins Page
1. Go to **Plugins** → **Installed Plugins**
2. Look for new columns: "Rank", "Pin", and "Company"
3. If you don't see them, check the debug information

### 4. Test Basic Functionality
1. **Test Ranking**: Enter a number (1-9999) in the Rank column for any plugin
2. **Test Pinning**: Click the star icon in the Pin column
3. **Test Company**: Enter a company name in the Company column
4. Check if changes are saved (you should see "Saved!" messages)

### 5. Check for Errors
1. Open browser developer tools (F12)
2. Check the Console tab for JavaScript errors
3. Check WordPress debug log at `/wp-content/debug.log`

## Common Issues and Solutions

### Issue: "Undefined array key 'Pin'" Error
**Possible Causes:**
- Plugin not fully loaded
- Hook conflicts with other plugins
- WordPress version compatibility

**Solutions:**
1. Deactivate all other plugins temporarily
2. Switch to a default WordPress theme
3. Check if error persists

### Issue: Columns Not Appearing
**Possible Causes:**
- JavaScript disabled
- Theme conflicts
- Plugin conflicts

**Solutions:**
1. Enable JavaScript in browser
2. Clear browser cache
3. Test with default theme

### Issue: AJAX Not Working
**Possible Causes:**
- JavaScript errors
- AJAX URL issues
- Nonce verification failures

**Solutions:**
1. Check browser console for errors
2. Verify AJAX URL in page source
3. Check WordPress debug log

## Debug Information

When debug mode is enabled, you'll see:
- Class loading status
- Database options status
- Hook registration status
- Current page information

## Manual Testing Checklist

- [ ] Plugin activates without errors
- [ ] Debug information shows all classes loaded
- [ ] Rank, Pin, and Company columns appear
- [ ] Rank input accepts numbers 0-9999
- [ ] Pin button toggles between filled/empty star
- [ ] Company input accepts text
- [ ] Changes are saved via AJAX
- [ ] Status messages appear ("Saving...", "Saved!")
- [ ] Pinned plugins are highlighted
- [ ] Sorting controls work
- [ ] Bulk actions work (pin/unpin multiple)

## Browser Testing

Test in these browsers:
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

## WordPress Version Testing

Test with:
- [ ] WordPress 6.0
- [ ] WordPress 6.1
- [ ] WordPress 6.2
- [ ] WordPress 6.3
- [ ] WordPress 6.4 (latest)

## Plugin Conflict Testing

Test with these common plugins:
- [ ] Yoast SEO
- [ ] WooCommerce
- [ ] Jetpack
- [ ] Wordfence
- [ ] Elementor

## Performance Testing

- [ ] Page load time not significantly increased
- [ ] AJAX responses are fast (< 1 second)
- [ ] No memory leaks in browser
- [ ] Database queries are optimized

## Security Testing

- [ ] AJAX requests require proper nonces
- [ ] User capability checks work
- [ ] Input is properly sanitized
- [ ] No SQL injection vulnerabilities

## Cleanup After Testing

To remove test data:
1. Deactivate the plugin
2. Delete the plugin
3. Check that all options are removed from database

## Reporting Issues

When reporting issues, include:
1. WordPress version
2. PHP version
3. Browser and version
4. Active plugins list
5. Current theme
6. Debug log contents
7. Steps to reproduce
8. Expected vs actual behavior

## Getting Help

1. Check the debug information first
2. Review browser console for JavaScript errors
3. Check WordPress debug log
4. Try with default theme and no other plugins
5. Create a staging site for testing
