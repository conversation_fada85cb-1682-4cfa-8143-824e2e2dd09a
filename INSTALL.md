# Plugin Ranker Installation Guide

## Quick Installation

### Method 1: WordPress Admin Upload (Recommended)

1. **Download the Plugin**
   - Download the plugin files as a ZIP archive
   - Ensure all files are included in the archive

2. **Upload via WordPress Admin**
   - Log in to your WordPress admin dashboard
   - Navigate to **Plugins** → **Add New**
   - Click **Upload Plugin**
   - Choose the plugin ZIP file
   - Click **Install Now**
   - Click **Activate Plugin**

### Method 2: FTP/SFTP Upload

1. **Extract Files**
   - Extract the plugin files to a folder named `plugin-ranker`

2. **Upload via FTP**
   - Connect to your website via FTP/SFTP
   - Navigate to `/wp-content/plugins/`
   - Upload the entire `plugin-ranker` folder

3. **Activate Plugin**
   - Log in to WordPress admin
   - Go to **Plugins** → **Installed Plugins**
   - Find "Plugin Ranker" and click **Activate**

### Method 3: WP-CLI (Advanced Users)

```bash
# Navigate to WordPress root directory
cd /path/to/wordpress

# Install the plugin (if available in repository)
wp plugin install plugin-ranker

# Or install from local ZIP file
wp plugin install /path/to/plugin-ranker.zip

# Activate the plugin
wp plugin activate plugin-ranker
```

## System Requirements

### Minimum Requirements
- **WordPress**: 6.0 or higher
- **PHP**: 7.4 or higher
- **MySQL**: 5.6 or higher (or MariaDB 10.1+)
- **Browser**: Modern browser with JavaScript enabled

### Recommended Requirements
- **WordPress**: 6.4 or higher
- **PHP**: 8.0 or higher
- **MySQL**: 8.0 or higher
- **Memory**: 128MB PHP memory limit
- **Browser**: Latest version of Chrome, Firefox, Safari, or Edge

## File Permissions

Ensure proper file permissions are set:

```bash
# Set directory permissions
find plugin-ranker/ -type d -exec chmod 755 {} \;

# Set file permissions
find plugin-ranker/ -type f -exec chmod 644 {} \;
```

## Verification

After installation, verify the plugin is working:

1. **Check Plugin Status**
   - Go to **Plugins** → **Installed Plugins**
   - Confirm "Plugin Ranker" shows as "Active"

2. **Test Functionality**
   - Navigate to **Plugins** → **Installed Plugins**
   - Look for new columns: "Rank", "Pin", and "Company"
   - Try entering a rank number and clicking the pin button

3. **Check for Errors**
   - Look for any error messages in WordPress admin
   - Check browser console for JavaScript errors
   - Review WordPress debug log if enabled

## Troubleshooting Installation

### Common Issues

#### Plugin Not Appearing
- **Cause**: Incorrect file structure or permissions
- **Solution**: Ensure the main plugin file is at `plugin-ranker/plugin-ranker.php`

#### Activation Errors
- **Cause**: PHP version incompatibility or missing dependencies
- **Solution**: Check PHP version and error logs

#### Missing Columns
- **Cause**: JavaScript conflicts or caching
- **Solution**: Clear cache, check for plugin conflicts

#### Permission Errors
- **Cause**: Insufficient user capabilities
- **Solution**: Ensure user has `activate_plugins` capability

### Debug Mode

Enable WordPress debug mode to troubleshoot issues:

```php
// Add to wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### Plugin Conflicts

If experiencing issues, check for plugin conflicts:

1. Deactivate all other plugins
2. Test Plugin Ranker functionality
3. Reactivate plugins one by one to identify conflicts

## Multisite Installation

For WordPress multisite networks:

### Network Activation
1. Upload plugin to `/wp-content/plugins/`
2. Go to **Network Admin** → **Plugins**
3. Click **Network Activate** for Plugin Ranker

### Individual Site Activation
1. Upload plugin to `/wp-content/plugins/`
2. Go to each site's **Plugins** page
3. Activate individually on desired sites

## Uninstallation

### Complete Removal
1. **Deactivate Plugin**
   - Go to **Plugins** → **Installed Plugins**
   - Click **Deactivate** for Plugin Ranker

2. **Delete Plugin**
   - Click **Delete** for Plugin Ranker
   - Confirm deletion

3. **Data Cleanup**
   - Plugin data is automatically removed during deletion
   - No manual database cleanup required

### Manual Cleanup (if needed)
If automatic cleanup fails, manually remove options:

```sql
DELETE FROM wp_options WHERE option_name LIKE 'plugin_ranker_%';
```

## Support

If you encounter installation issues:

1. **Check Requirements**: Verify your system meets minimum requirements
2. **Review Logs**: Check WordPress and server error logs
3. **Test Environment**: Try installation on a staging site first
4. **Contact Support**: Create an issue in the plugin repository

## Security Considerations

- Keep WordPress and PHP updated
- Use strong passwords and two-factor authentication
- Regular backups before plugin installations
- Test on staging environment first
- Monitor for security updates

## Performance Notes

Plugin Ranker is designed to be lightweight:
- Minimal database queries
- Efficient AJAX operations
- Optimized CSS and JavaScript
- No impact on frontend performance

The plugin only loads resources on the WordPress admin plugins page, ensuring no performance impact on your website's frontend.
