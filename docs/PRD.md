# Plugin Ranker - Product Requirements Document

## Project Overview
Create a WordPress plugin that provides a simple Tools page for managing plugin rankings through drag-and-drop interface.

## Key Requirements
- Target WordPress 6.x and above
- Add a menu item under WordPress Tools called "Plugin Ranker"
- Provide drag-and-drop interface to reorder plugins
- Store plugin rankings in wp_options table
- Use AJAX for saving changes
- Simple, clean interface with minimal features

## Core Features
1. **Tools Menu Page**: Single page under Tools → Plugin Ranker
2. **Drag & Drop Interface**: Reorder plugins by dragging
3. **Auto-ranking**: Assign ranks 1, 2, 3... based on drag order
4. **Save Functionality**: AJAX save of new order
5. **Plugin List**: Show all installed plugins with basic info

## Technical Requirements
- Use WordPress admin UI standards
- jQuery UI Sortable for drag-and-drop
- WordPress nonces for security
- Store data in wp_options table
- No modifications to existing WordPress pages

## Technical Specifications

### Core Features
1. **Plugin Ranking System**
   - Allow users to assign custom serial numbers/ranks to plugins
   - Display rank column in plugins table
   - Enable sorting by rank

2. **Pin Functionality**
   - <PERSON><PERSON> frequently used plugins to the top of the list
   - Visual indicator for pinned plugins
   - Toggle pin state with AJAX

3. **Company/Developer Sorting**
   - Add company column to plugins table
   - Extract developer information from plugin headers
   - Enable sorting by company name

4. **Data Persistence**
   - Store all preferences in wp_options table
   - Maintain settings across sessions
   - Support both single-site and multisite environments

### Technical Implementation
- **Backend**: PHP following WordPress coding standards
- **Frontend**: JavaScript/jQuery for admin UI interactions
- **Storage**: WordPress wp_options table
- **AJAX**: For real-time updates without page reload
- **Security**: Proper nonces, sanitization, and validation

### Compatibility
- WordPress 6.x and above
- Single-site and multisite environments
- Modern browsers with JavaScript enabled

## Success Criteria
- Plugin successfully enhances the WordPress Plugins admin page
- Users can assign ranks, pin plugins, and sort by company
- All preferences persist across sessions
- Plugin follows WordPress best practices and security standards
- Compatible with target WordPress versions and environments
