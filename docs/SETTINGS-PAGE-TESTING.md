# Plugin Ranker Settings Page - Testing Guide

## Quick Testing Steps

### 1. Access the Settings Page
1. Go to **WordPress Admin → Settings → Plugin Ranker**
2. Verify the page loads without errors
3. Check that the page title shows "Plugin Ranker Settings"

### 2. Test Fetch All Plugins
1. Click the "Fetch All Plugins" button
2. Confirm the confirmation dialog appears
3. Click "OK" to proceed
4. Verify plugins are loaded in the sortable list
5. Check that plugins show correct information (name, description, status)

### 3. Test Drag & Drop Functionality
1. Locate the drag handle (≡ icon) next to any plugin
2. Click and hold the drag handle
3. Drag the plugin to a different position
4. Verify the placeholder appears during dragging
5. Release to drop the plugin
6. Check that rank numbers update automatically

### 4. Test Save Functionality
1. After reordering plugins, click "Save Order"
2. Verify the "Saving..." status appears
3. Check for "Order saved successfully!" message
4. Navigate to **Plugins → Installed Plugins**
5. Verify the rankings are reflected in the Rank column

### 5. Visual Elements Testing
1. **Active Plugins**: Should have green "Active" badge
2. **Pinned Plugins**: Should have yellow "Pinned" badge with star icon
3. **Rank Numbers**: Should be in blue circular badges
4. **Drag Handle**: Should change color on hover
5. **Plugin Items**: Should highlight on hover

## Expected Behavior

### Fetch All Plugins
- ✅ Shows confirmation dialog
- ✅ Displays loading state
- ✅ Populates plugin list
- ✅ Shows success message
- ✅ Initializes drag-and-drop

### Drag & Drop
- ✅ Drag handle is clickable
- ✅ Plugin item follows cursor during drag
- ✅ Placeholder shows drop target
- ✅ Rank numbers update after drop
- ✅ Visual feedback during drag

### Save Order
- ✅ Shows loading state
- ✅ Sends AJAX request
- ✅ Displays success/error message
- ✅ Updates database rankings
- ✅ Reflects changes on plugins page

## Browser Testing Checklist

### Desktop Browsers
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### Mobile Testing
- [ ] Touch drag-and-drop works
- [ ] Responsive layout adapts
- [ ] Buttons are touch-friendly
- [ ] Text is readable

## Functionality Testing

### Basic Operations
- [ ] Page loads without errors
- [ ] Fetch button works
- [ ] Drag-and-drop functions
- [ ] Save button works
- [ ] Status messages appear
- [ ] Rankings persist after save

### Edge Cases
- [ ] Empty plugin list handling
- [ ] Network error handling
- [ ] Permission error handling
- [ ] Large plugin lists (50+ plugins)
- [ ] Rapid drag operations
- [ ] Multiple save operations

### Integration Testing
- [ ] Rankings appear on main plugins page
- [ ] Pin status is preserved
- [ ] Company information is maintained
- [ ] Bulk actions still work
- [ ] No conflicts with existing functionality

## Error Scenarios

### Network Issues
- [ ] AJAX timeout handling
- [ ] Server error responses
- [ ] Connection loss during save

### Permission Issues
- [ ] Non-admin user access
- [ ] Capability check enforcement
- [ ] Nonce verification

### Data Issues
- [ ] Invalid plugin data
- [ ] Corrupted rankings
- [ ] Missing plugins

## Performance Testing

### Load Times
- [ ] Page loads in < 2 seconds
- [ ] Fetch operation completes quickly
- [ ] Save operation is responsive
- [ ] No memory leaks during drag operations

### Large Lists
- [ ] 50+ plugins load smoothly
- [ ] Drag performance remains good
- [ ] Save operation handles large datasets
- [ ] UI remains responsive

## Accessibility Testing

### Keyboard Navigation
- [ ] Tab navigation works
- [ ] Enter/Space activate buttons
- [ ] Focus indicators visible
- [ ] Screen reader compatibility

### Visual Accessibility
- [ ] High contrast mode support
- [ ] Color blind friendly
- [ ] Sufficient color contrast
- [ ] Readable font sizes

## Common Issues & Solutions

### Drag-and-Drop Not Working
**Symptoms**: Can't drag plugins, no visual feedback
**Check**: 
- JavaScript console for errors
- jQuery UI Sortable is loaded
- Browser supports drag events

**Solution**: Clear cache, check for plugin conflicts

### Save Not Working
**Symptoms**: Save button doesn't respond, no status messages
**Check**:
- Network tab for AJAX requests
- WordPress debug log
- User permissions

**Solution**: Check nonce, verify user capabilities

### Plugins Not Loading
**Symptoms**: Empty list after fetch, error messages
**Check**:
- WordPress debug log
- Plugin permissions
- Database connectivity

**Solution**: Check file permissions, verify plugin data

### Visual Issues
**Symptoms**: Layout broken, missing styles
**Check**:
- CSS file loading
- Browser developer tools
- Theme conflicts

**Solution**: Clear cache, check for CSS conflicts

## Debug Information

### Enable Debug Mode
Add to `wp-config.php`:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
define('WP_DEBUG_DISPLAY', false);
```

### Check Debug Log
Location: `/wp-content/debug.log`
Look for: "Plugin Ranker" entries

### Browser Console
Check for:
- JavaScript errors
- AJAX request failures
- Network timeouts

## Reporting Issues

When reporting problems, include:
1. WordPress version
2. Browser and version
3. Steps to reproduce
4. Expected vs actual behavior
5. Console errors (if any)
6. Debug log entries
7. List of active plugins
8. Current theme

## Success Criteria

The settings page is working correctly when:
- ✅ All plugins can be fetched and displayed
- ✅ Drag-and-drop reordering works smoothly
- ✅ Rankings save and persist correctly
- ✅ Visual feedback is clear and helpful
- ✅ No JavaScript errors in console
- ✅ Changes reflect on main plugins page
- ✅ Performance is acceptable for large plugin lists
- ✅ Mobile/touch devices work properly
