# Plugin Ranker Settings Page

## Overview

The Plugin Ranker Settings Page provides a dedicated interface for managing plugin rankings through an intuitive drag-and-drop system. This page is completely separate from the WordPress plugins page and offers advanced ranking management features.

## Location

The settings page can be found at:
**WordPress Admin → Tools → Plugin Ranker**

## Features

### 1. Fetch All Plugins
- **Purpose**: Automatically retrieves all installed plugins and populates them in the ranking list
- **Usage**: Click the "Fetch All Plugins" button to load all plugins
- **Confirmation**: Shows a confirmation dialog before fetching
- **Result**: Displays all plugins in their current ranking order

### 2. Drag & Drop Interface
- **Purpose**: Allows visual reordering of plugins through drag-and-drop
- **Usage**: 
  - Grab the drag handle (≡ icon) on the left of each plugin
  - Drag plugins up or down to reorder them
  - Rankings are automatically updated based on position
- **Visual Feedback**: 
  - Drag placeholder shows where the plugin will be dropped
  - Helper element provides visual feedback during dragging
  - Rank numbers update automatically after reordering

### 3. Plugin Information Display
Each plugin item shows:
- **Rank Number**: Circular badge showing the current rank
- **Plugin Name**: The display name of the plugin
- **Status Badges**: 
  - "Active" for currently active plugins
  - "Pinned" for plugins marked as pinned
- **Description**: Brief description of the plugin
- **Metadata**: Author and version information

### 4. Save Functionality
- **Manual Save**: Click "Save Order" to persist changes
- **Auto-save**: Optional automatic saving on every reorder (currently disabled)
- **Feedback**: Status messages show save progress and results

## User Interface Elements

### Controls Section
- **Fetch All Plugins Button**: Loads all installed plugins
- **Save Order Button**: Saves the current plugin order
- **Status Messages**: Shows operation feedback

### Plugin List
- **Drag Handle**: Three-line icon for dragging
- **Rank Badge**: Numbered circle showing position
- **Plugin Info**: Name, description, and metadata
- **Visual States**: Different styling for active and pinned plugins

## Technical Implementation

### AJAX Operations
- **Fetch Plugins**: `plugin_ranker_fetch_plugins`
- **Save Order**: `plugin_ranker_save_order`
- **Security**: All requests use WordPress nonces

### JavaScript Features
- **jQuery UI Sortable**: Handles drag-and-drop functionality
- **Real-time Updates**: Rank numbers update during dragging
- **Error Handling**: Graceful handling of AJAX failures
- **User Feedback**: Loading states and status messages

### CSS Features
- **Responsive Design**: Works on desktop and mobile
- **Visual Feedback**: Hover states and drag animations
- **Accessibility**: High contrast and keyboard navigation support
- **Dark Mode**: Automatic dark mode support

## Usage Instructions

### Initial Setup
1. Navigate to **Settings → Plugin Ranker**
2. Click "Fetch All Plugins" to populate the list
3. Plugins will appear in their current ranking order

### Reordering Plugins
1. Locate the plugin you want to move
2. Click and hold the drag handle (≡ icon)
3. Drag the plugin to its new position
4. Release to drop the plugin
5. Rank numbers will update automatically
6. Click "Save Order" to persist changes

### Understanding Visual Cues
- **Blue border**: Indicates drag hover state
- **Highlighted background**: Shows pinned plugins
- **Green badge**: Indicates active plugins
- **Yellow badge**: Indicates pinned plugins
- **Animated placeholder**: Shows drop target during drag

## Security Features

### Permission Checks
- Requires `manage_options` capability
- Only administrators can access the settings page

### Data Validation
- Plugin file names are sanitized
- Rank values are validated as integers
- AJAX requests include nonce verification

### Error Handling
- Graceful degradation for JavaScript failures
- Comprehensive error messages
- Debug logging when WP_DEBUG is enabled

## Compatibility

### WordPress Versions
- WordPress 6.0 and above
- Tested with latest WordPress versions

### Browser Support
- Modern browsers with JavaScript enabled
- jQuery UI Sortable support required
- Touch device support for mobile

### Plugin Conflicts
- Isolated from other plugin functionality
- No modifications to core WordPress plugins page
- Separate CSS and JavaScript namespacing

## Troubleshooting

### Common Issues

#### Drag-and-Drop Not Working
- **Check**: JavaScript is enabled
- **Check**: jQuery UI is loaded
- **Solution**: Clear browser cache and reload

#### Plugins Not Loading
- **Check**: User has proper permissions
- **Check**: WordPress debug log for errors
- **Solution**: Try deactivating other plugins

#### Save Not Working
- **Check**: AJAX requests in browser network tab
- **Check**: WordPress debug log for errors
- **Solution**: Verify nonce and permissions

### Debug Information
Enable WordPress debug mode to see:
- AJAX request details
- PHP error messages
- Plugin loading status
- Database operation results

## Future Enhancements

### Planned Features
- Auto-save on reorder
- Bulk operations (pin/unpin multiple)
- Import/export rankings
- Plugin categories and filtering
- Search and filter functionality
- Undo/redo operations

### Performance Optimizations
- Lazy loading for large plugin lists
- Debounced save operations
- Optimized database queries
- Caching for plugin metadata

## Developer Notes

### Extending Functionality
The settings page is built with extensibility in mind:
- Modular JavaScript architecture
- Hookable PHP methods
- Customizable CSS variables
- Event-driven updates

### Code Organization
- `admin/class-settings-page.php`: Main settings page class
- `assets/css/settings.css`: Settings page styles
- `assets/js/settings.js`: Settings page JavaScript
- Follows WordPress coding standards
