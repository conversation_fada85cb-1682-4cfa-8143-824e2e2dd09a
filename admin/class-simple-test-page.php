<?php
/**
 * Simple Test Page for Plugin Ranker
 * 
 * This is a minimal implementation to test if the basic menu registration works
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PluginRanker_Simple_Test_Page {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_test_menu'));
    }
    
    public function add_test_menu() {
        // Try the most basic menu registration possible
        add_menu_page(
            'Plugin Ranker Test',
            'PR Test',
            'read',
            'plugin-ranker-test',
            array($this, 'test_page_content'),
            'dashicons-admin-plugins',
            30
        );
        
        // Also try submenu under Tools
        add_submenu_page(
            'tools.php',
            'Plugin Ranker Test',
            'PR Test Tools',
            'read',
            'plugin-ranker-test-tools',
            array($this, 'test_page_content')
        );
        
        // And under Settings
        add_submenu_page(
            'options-general.php',
            'Plugin Ranker Test',
            'PR Test Settings',
            'read',
            'plugin-ranker-test-settings',
            array($this, 'test_page_content')
        );
    }
    
    public function test_page_content() {
        ?>
        <div class="wrap">
            <h1>Plugin Ranker Test Page</h1>
            <p>If you can see this page, the basic menu registration is working!</p>
            
            <h2>User Information</h2>
            <?php
            $current_user = wp_get_current_user();
            echo '<p><strong>User ID:</strong> ' . $current_user->ID . '</p>';
            echo '<p><strong>User Login:</strong> ' . $current_user->user_login . '</p>';
            echo '<p><strong>User Roles:</strong> ' . implode(', ', $current_user->roles) . '</p>';
            
            echo '<h2>Capabilities</h2>';
            echo '<p><strong>read:</strong> ' . (current_user_can('read') ? 'YES' : 'NO') . '</p>';
            echo '<p><strong>edit_posts:</strong> ' . (current_user_can('edit_posts') ? 'YES' : 'NO') . '</p>';
            echo '<p><strong>activate_plugins:</strong> ' . (current_user_can('activate_plugins') ? 'YES' : 'NO') . '</p>';
            echo '<p><strong>manage_options:</strong> ' . (current_user_can('manage_options') ? 'YES' : 'NO') . '</p>';
            ?>
            
            <h2>Test Links</h2>
            <p>Try accessing the original Plugin Ranker page:</p>
            <p><a href="<?php echo admin_url('options-general.php?page=plugin-ranker'); ?>" class="button">Original Plugin Ranker Settings</a></p>
            <p><a href="<?php echo admin_url('tools.php?page=plugin-ranker'); ?>" class="button">Try Tools Menu</a></p>
        </div>
        <?php
    }
}
