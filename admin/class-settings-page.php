<?php
/**
 * Plugin Ranker Tools Page
 *
 * Simple Tools page for plugin ranking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PluginRanker_Settings_Page {

    private $page_hook;

    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_plugin_ranker_save_order', array($this, 'save_order'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        $this->page_hook = add_options_page(
            __('Plugin Ranker', 'plugin-ranker'),
            __('Plugin Ranker', 'plugin-ranker'),
            'manage_options',
            'plugin-ranker',
            array($this, 'admin_page')
        );
    }
    
    /**
     * Enqueue scripts
     */
    public function enqueue_scripts($hook) {
        if ($hook !== $this->page_hook) {
            return;
        }

        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_style('plugin-ranker-admin', PLUGIN_RANKER_PLUGIN_URL . 'assets/css/admin.css', array(), '1.0.0');

        wp_localize_script('jquery-ui-sortable', 'pluginRankerAjax', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('plugin_ranker_nonce')
        ));
    }
    
    /**
     * Admin page callback
     */
    public function admin_page() {
        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        $plugins = get_plugins();
        ?>
        <div class="wrap">
            <h1>Plugin Ranker</h1>
            <p>Drag and drop to reorder plugins. Click Save to update rankings.</p>

            <button type="button" id="save-order" class="button button-primary">Save Order</button>
            <span id="save-status"></span>

            <ul id="plugin-list" style="margin-top: 20px;">
                <?php foreach ($plugins as $plugin_file => $plugin_data): ?>
                    <li data-plugin="<?php echo esc_attr($plugin_file); ?>" style="padding: 10px; margin: 5px 0; background: #f9f9f9; border: 1px solid #ddd; cursor: move;">
                        <strong><?php echo esc_html($plugin_data['Name']); ?></strong>
                        <br><small><?php echo esc_html($plugin_data['Description']); ?></small>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#plugin-list').sortable({
                cursor: 'move',
                opacity: 0.8
            });

            $('#save-order').click(function() {
                var order = [];
                $('#plugin-list li').each(function(index) {
                    order.push($(this).data('plugin'));
                });

                $('#save-status').text('Saving...');

                $.post(ajaxurl, {
                    action: 'plugin_ranker_save_order',
                    order: order,
                    nonce: pluginRankerAjax.nonce
                }, function(response) {
                    $('#save-status').text('Saved!').delay(2000).fadeOut();
                });
            });
        });
        </script>
        <?php
    }
    
    /**
     * Save order
     */
    public function save_order() {
        if (!wp_verify_nonce($_POST['nonce'], 'plugin_ranker_nonce') || !current_user_can('manage_options')) {
            wp_die('Security check failed');
        }

        $order = $_POST['order'];
        if (is_array($order)) {
            $ranks = array();
            foreach ($order as $index => $plugin_file) {
                $ranks[sanitize_text_field($plugin_file)] = $index + 1;
            }
            update_option('plugin_ranker_plugin_ranks', $ranks);
        }

        wp_send_json_success();
    }
}
