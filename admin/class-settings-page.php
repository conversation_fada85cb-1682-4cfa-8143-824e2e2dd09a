<?php
/**
 * Plugin Ranker Tools Page
 * Simple Tools page for plugin ranking
 */

if (!defined('ABSPATH')) {
    exit;
}

class PluginRanker_Settings_Page {

    private $page_hook;

    public function __construct() {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Plugin Ranker: Settings page constructor called');
        }

        add_action('admin_menu', array($this, 'add_admin_menu'), 10);
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_ajax_plugin_ranker_save_order', array($this, 'save_order'));
    }

    /**
     * Add admin menu (single capability: manage_options)
     */
    public function add_admin_menu() {
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Plugin Ranker: Attempting to register admin menu. Current user can manage_options: ' .
                (current_user_can('manage_options') ? 'YES' : 'NO'));
        }

        $this->page_hook = add_management_page(
            __('Plugin Ranker', 'plugin-ranker'),
            __('Plugin Ranker', 'plugin-ranker'),
            'manage_options', // unified capability
            'plugin-ranker-tools',
            array($this, 'admin_page')
        );

        if (defined('WP_DEBUG') && WP_DEBUG) {
            if ($this->page_hook) {
                error_log('Plugin Ranker: Settings page hook created successfully: ' . $this->page_hook);
            } else {
                error_log('Plugin Ranker: Failed to create settings page hook');
            }
        }
    }

    /**
     * Enqueue scripts
     */
    public function enqueue_scripts($hook) {
        if ($hook !== $this->page_hook) {
            return;
        }

        wp_enqueue_script('jquery');
        wp_enqueue_script('jquery-ui-sortable');
        wp_enqueue_style('plugin-ranker-admin', PLUGIN_RANKER_PLUGIN_URL . 'assets/css/admin.css', array(), '1.0.0');

        wp_add_inline_script(
            'jquery-ui-sortable',
            'var pluginRankerAjax = ' . json_encode(array(
                'ajaxurl' => admin_url('admin-ajax.php'),
                'nonce'   => wp_create_nonce('plugin_ranker_nonce')
            )) . ';',
            'before'
        );
    }

    /**
     * Admin page callback
     */
    public function admin_page() {
        if (!current_user_can('manage_options')) {
            wp_die(__('Sorry, you are not allowed to access this page.', 'plugin-ranker'));
        }

        if (!function_exists('get_plugins')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }

        $plugins = get_plugins();
        ?>
        <div class="wrap">
            <h1><?php esc_html_e('Plugin Ranker', 'plugin-ranker'); ?></h1>
            <p><?php esc_html_e('Drag and drop to reorder plugins. Click Save to update rankings.', 'plugin-ranker'); ?></p>

            <button type="button" id="save-order" class="button button-primary"><?php esc_html_e('Save Order', 'plugin-ranker'); ?></button>
            <span id="save-status"></span>

            <ul id="plugin-list" style="margin-top: 20px;">
                <?php foreach ($plugins as $plugin_file => $plugin_data): ?>
                    <li data-plugin="<?php echo esc_attr($plugin_file); ?>" style="padding: 10px; margin: 5px 0; background: #f9f9f9; border: 1px solid #ddd; cursor: move;">
                        <strong><?php echo esc_html($plugin_data['Name']); ?></strong>
                        <br><small><?php echo esc_html($plugin_data['Description']); ?></small>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#plugin-list').sortable({
                cursor: 'move',
                opacity: 0.8
            });

            $('#save-order').click(function() {
                var order = [];
                $('#plugin-list li').each(function(index) {
                    order.push($(this).data('plugin'));
                });

                $('#save-status').text('Saving...');

                $.post(pluginRankerAjax.ajaxurl, {
                    action: 'plugin_ranker_save_order',
                    order: order,
                    nonce: pluginRankerAjax.nonce
                }, function(response) {
                    if (response.success) {
                        $('#save-status').text('Saved!').delay(2000).fadeOut();
                    } else {
                        $('#save-status').text('Error: ' + (response.data || 'Unknown error')).css('color', '#dc3232');
                    }
                }).fail(function() {
                    $('#save-status').text('Error: Failed to save').css('color', '#dc3232');
                });
            });
        });
        </script>
        <?php
    }

    /**
     * Save order
     */
    public function save_order() {
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'plugin_ranker_nonce')) {
            wp_send_json_error(__('Security check failed: Invalid nonce', 'plugin-ranker'));
        }

        if (!current_user_can('manage_options')) {
            wp_send_json_error(__('Security check failed: Insufficient permissions', 'plugin-ranker'));
        }

        if (!isset($_POST['order']) || !is_array($_POST['order'])) {
            wp_send_json_error(__('Invalid order data', 'plugin-ranker'));
        }

        $order = $_POST['order'];
        $ranks = array();
        foreach ($order as $index => $plugin_file) {
            $ranks[sanitize_text_field($plugin_file)] = $index + 1;
        }

        if (update_option('plugin_ranker_plugin_ranks', $ranks)) {
            wp_send_json_success(__('Plugin order saved successfully', 'plugin-ranker'));
        } else {
            wp_send_json_error(__('Failed to save plugin order', 'plugin-ranker'));
        }
    }
}

// Initialize
new PluginRanker_Settings_Page();
