# Plugin Ranker

A WordPress plugin that provides a dedicated settings page for managing plugin rankings through an intuitive drag-and-drop interface.

## Features

- **Dedicated Settings Page**: Clean, dedicated interface for managing plugin rankings
- **Drag & Drop Reordering**: Visual plugin reordering with automatic rank assignment
- **Fetch All Plugins**: Automatically load all installed plugins into the ranking system
- **Custom Plugin Rankings**: Assign numerical ranks based on drag-and-drop order
- **Pin Status Display**: Visual indicators for pinned plugins
- **Plugin Information**: Display plugin names, descriptions, authors, and versions
- **Persistent Settings**: All rankings are saved and persist across sessions
- **AJAX Interface**: Real-time updates without page reloads
- **Responsive Design**: Works on desktop and mobile devices
- **Multisite Compatible**: Works with both single-site and multisite WordPress installations

## Requirements

- WordPress 6.0 or higher
- PHP 7.4 or higher
- Modern browser with JavaScript enabled

## Installation

1. Download the plugin files
2. Upload the `plugin-ranker` folder to your `/wp-content/plugins/` directory
3. Activate the plugin through the 'Plugins' menu in WordPress
4. Navigate to the Plugins page to start using the new features

## Usage

### Accessing the Tools Page

The Plugin Ranker page can be found at:
**WordPress Admin → Tools → Plugin Ranker**

### Managing Plugin Rankings

1. **Navigate to the Settings Page**: Go to one of the locations mentioned above
2. **Load Plugins**: Click "Fetch All Plugins" to load all installed plugins into the interface
3. **Reorder Plugins**: Use the drag-and-drop interface to reorder plugins:
   - Grab the drag handle (≡ icon) next to each plugin
   - Drag plugins up or down to reorder them
   - Rankings are automatically assigned based on position (1, 2, 3, etc.)
4. **Save Changes**: Click "Save Order" to persist your changes to the database

### Plugin Information Display

Each plugin in the list shows:
- **Rank Number**: Blue circular badge showing the current position
- **Plugin Name**: The display name of the plugin
- **Status Badges**:
  - Green "Active" badge for currently active plugins
  - Yellow "Pinned" badge with star icon for pinned plugins
- **Description**: Brief description of what the plugin does
- **Author & Version**: Plugin developer and version information

### Visual Feedback

- **Drag Handle**: Three-line icon (≡) that changes color on hover
- **Drag Placeholder**: Shows where the plugin will be dropped during dragging
- **Status Messages**: Real-time feedback for save operations
- **Responsive Design**: Interface adapts to mobile and tablet screens

## Technical Details

### Data Storage

All plugin data is stored in the WordPress `wp_options` table:
- `plugin_ranker_plugin_ranks`: Stores plugin rank assignments
- `plugin_ranker_pinned_plugins`: Stores pinned plugin list
- `plugin_ranker_plugin_companies`: Stores company information

### File Structure

```
plugin-ranker/
├── plugin-ranker.php          # Main plugin file
├── docs/
│   └── PRD.md                  # Product Requirements Document
├── includes/
│   └── class-plugin-data-manager.php  # Data management class
├── admin/
│   └── class-settings-page.php        # Tools page class
├── assets/
│   └── css/
│       └── admin.css          # Simple styles
└── README.md                  # This file
```

### Hooks and Filters

The plugin uses standard WordPress hooks:
- `manage_plugins_columns` - Add custom columns
- `manage_plugins_custom_column` - Display column content
- `manage_plugins_sortable_columns` - Make columns sortable
- `wp_ajax_*` - Handle AJAX requests

## Security

- All AJAX requests are protected with WordPress nonces
- User capability checks ensure only users with `activate_plugins` permission can make changes
- All input is sanitized and validated
- SQL injection protection through WordPress APIs

## Browser Support

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Internet Explorer 11 (limited support)

## Troubleshooting

### Plugin data not saving
- Check that you have the `activate_plugins` capability
- Ensure JavaScript is enabled in your browser
- Check browser console for JavaScript errors

### Columns not appearing
- Try deactivating and reactivating the plugin
- Clear any caching plugins
- Check for theme/plugin conflicts

### Sorting not working
- Ensure JavaScript is enabled
- Check for JavaScript conflicts with other plugins
- Try refreshing the page

## Development

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes following WordPress coding standards
4. Test thoroughly
5. Submit a pull request

### Coding Standards

This plugin follows:
- WordPress PHP Coding Standards
- WordPress JavaScript Coding Standards
- WordPress CSS Coding Standards

## Changelog

### Version 1.0.0
- Initial release
- Custom plugin ranking system
- Pin functionality
- Company sorting
- AJAX interface
- Bulk actions
- Responsive design

## License

This plugin is licensed under the GPL v2 or later.

## Support

For support, please create an issue in the plugin repository or contact the plugin author.

## Credits

Developed following WordPress best practices and coding standards.
