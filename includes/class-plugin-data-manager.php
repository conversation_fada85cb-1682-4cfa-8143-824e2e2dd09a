<?php
/**
 * Plugin Data Manager Class
 * 
 * Handles all data operations for plugin rankings, pins, and company information
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class PluginRanker_Data_Manager {
    
    /**
     * Option names
     */
    const OPTION_RANKS = 'plugin_ranker_plugin_ranks';
    const OPTION_PINNED = 'plugin_ranker_pinned_plugins';
    const OPTION_COMPANIES = 'plugin_ranker_plugin_companies';
    
    /**
     * Get plugin rank
     */
    public static function get_plugin_rank($plugin_file) {
        $ranks = get_option(self::OPTION_RANKS, array());
        return isset($ranks[$plugin_file]) ? intval($ranks[$plugin_file]) : 0;
    }
    
    /**
     * Set plugin rank
     */
    public static function set_plugin_rank($plugin_file, $rank) {
        $ranks = get_option(self::OPTION_RANKS, array());
        
        if ($rank > 0) {
            $ranks[$plugin_file] = intval($rank);
        } else {
            unset($ranks[$plugin_file]);
        }
        
        return update_option(self::OPTION_RANKS, $ranks);
    }
    
    /**
     * Get all plugin ranks
     */
    public static function get_all_ranks() {
        return get_option(self::OPTION_RANKS, array());
    }
    
    /**
     * Check if plugin is pinned
     */
    public static function is_plugin_pinned($plugin_file) {
        $pinned = get_option(self::OPTION_PINNED, array());
        return in_array($plugin_file, $pinned);
    }
    
    /**
     * Pin plugin
     */
    public static function pin_plugin($plugin_file) {
        $pinned = get_option(self::OPTION_PINNED, array());
        
        if (!in_array($plugin_file, $pinned)) {
            $pinned[] = $plugin_file;
            return update_option(self::OPTION_PINNED, $pinned);
        }
        
        return true;
    }
    
    /**
     * Unpin plugin
     */
    public static function unpin_plugin($plugin_file) {
        $pinned = get_option(self::OPTION_PINNED, array());
        $key = array_search($plugin_file, $pinned);
        
        if ($key !== false) {
            unset($pinned[$key]);
            $pinned = array_values($pinned); // Re-index array
            return update_option(self::OPTION_PINNED, $pinned);
        }
        
        return true;
    }
    
    /**
     * Toggle pin status
     */
    public static function toggle_pin($plugin_file) {
        if (self::is_plugin_pinned($plugin_file)) {
            return self::unpin_plugin($plugin_file);
        } else {
            return self::pin_plugin($plugin_file);
        }
    }
    
    /**
     * Get all pinned plugins
     */
    public static function get_pinned_plugins() {
        return get_option(self::OPTION_PINNED, array());
    }
    
    /**
     * Get plugin company
     */
    public static function get_plugin_company($plugin_file) {
        $companies = get_option(self::OPTION_COMPANIES, array());
        
        if (isset($companies[$plugin_file])) {
            return $companies[$plugin_file];
        }
        
        // Try to extract from plugin data
        $company = self::extract_company_from_plugin($plugin_file);
        if ($company) {
            self::set_plugin_company($plugin_file, $company);
            return $company;
        }
        
        return '';
    }
    
    /**
     * Set plugin company
     */
    public static function set_plugin_company($plugin_file, $company) {
        $companies = get_option(self::OPTION_COMPANIES, array());
        
        if (!empty($company)) {
            $companies[$plugin_file] = sanitize_text_field($company);
        } else {
            unset($companies[$plugin_file]);
        }
        
        return update_option(self::OPTION_COMPANIES, $companies);
    }
    
    /**
     * Extract company from plugin data
     */
    private static function extract_company_from_plugin($plugin_file) {
        if (!function_exists('get_plugin_data')) {
            require_once ABSPATH . 'wp-admin/includes/plugin.php';
        }
        
        $plugin_data = get_plugin_data(WP_PLUGIN_DIR . '/' . $plugin_file);
        
        // Try to extract company from Author field
        $author = $plugin_data['Author'];
        if (!empty($author)) {
            // Remove HTML tags and get clean author name
            $author = wp_strip_all_tags($author);
            return $author;
        }
        
        // Try to extract from AuthorURI
        $author_uri = $plugin_data['AuthorURI'];
        if (!empty($author_uri)) {
            $parsed_url = parse_url($author_uri);
            if (isset($parsed_url['host'])) {
                $host = $parsed_url['host'];
                // Remove www. prefix
                $host = preg_replace('/^www\./', '', $host);
                return $host;
            }
        }
        
        return '';
    }
    
    /**
     * Get all plugin companies
     */
    public static function get_all_companies() {
        return get_option(self::OPTION_COMPANIES, array());
    }
    
    /**
     * Sort plugins by criteria
     */
    public static function sort_plugins($plugins, $sort_by = 'rank') {
        switch ($sort_by) {
            case 'rank':
                return self::sort_by_rank($plugins);
            case 'company':
                return self::sort_by_company($plugins);
            case 'pinned':
                return self::sort_by_pinned($plugins);
            default:
                return $plugins;
        }
    }
    
    /**
     * Sort plugins by rank
     */
    private static function sort_by_rank($plugins) {
        $ranks = self::get_all_ranks();
        
        uksort($plugins, function($a, $b) use ($ranks) {
            $rank_a = isset($ranks[$a]) ? $ranks[$a] : 999999;
            $rank_b = isset($ranks[$b]) ? $ranks[$b] : 999999;
            
            if ($rank_a === $rank_b) {
                return strcmp($a, $b);
            }
            
            return $rank_a - $rank_b;
        });
        
        return $plugins;
    }
    
    /**
     * Sort plugins by company
     */
    private static function sort_by_company($plugins) {
        uksort($plugins, function($a, $b) {
            $company_a = self::get_plugin_company($a);
            $company_b = self::get_plugin_company($b);
            
            if ($company_a === $company_b) {
                return strcmp($a, $b);
            }
            
            return strcmp($company_a, $company_b);
        });
        
        return $plugins;
    }
    
    /**
     * Sort plugins by pinned status
     */
    private static function sort_by_pinned($plugins) {
        $pinned = self::get_pinned_plugins();
        
        uksort($plugins, function($a, $b) use ($pinned) {
            $pinned_a = in_array($a, $pinned);
            $pinned_b = in_array($b, $pinned);
            
            if ($pinned_a === $pinned_b) {
                return strcmp($a, $b);
            }
            
            return $pinned_b - $pinned_a; // Pinned items first
        });
        
        return $plugins;
    }
    
    /**
     * Clear all plugin data
     */
    public static function clear_all_data() {
        delete_option(self::OPTION_RANKS);
        delete_option(self::OPTION_PINNED);
        delete_option(self::OPTION_COMPANIES);
    }
}
